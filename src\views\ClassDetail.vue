<template>
  <div class="min-h-screen flex flex-col bg-white">
    <NavbarStudent />

    <!-- Breadcrumbs -->
    <div class="w-full px-4 sm:px-6 md:px-8 pt-6 pb-4">
      <div class="flex items-center text-gray-600">
        <router-link to="/student/classes" class="text-gray-600 hover:text-orange transition-colors">Classes Camp</router-link>
        <svg class="w-4 h-4 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
        <span class="text-orange font-medium">{{ classData.title }}</span>
      </div>
    </div>

    <!-- Main Content -->
    <div class="w-full px-4 sm:px-6 md:px-8 py-4 flex-grow">
      <!-- Class Header Section -->
      <div class="flex flex-col lg:flex-row gap-6 mb-8">
        <!-- Left Column - Class Image -->
        <div class="lg:w-1/3">
          <div class="bg-orange rounded-lg overflow-hidden h-full">
            <img
              :src="getClassImage(classData)"
              :alt="classData.title"
              class="w-full h-full object-cover"
              @error="handleImageError"
              ref="classImage"
              style="max-height: 400px;"
            />
          </div>
        </div>

        <!-- Right Column - Class Info -->
        <div class="lg:w-2/3 relative flex flex-col">
          <!-- Class Title -->
          <h1 class="text-2xl font-bold text-gray-800 mb-4">{{ classData.title }}</h1>

          <!-- Class Stats -->
          <div class="flex flex-wrap items-center gap-4 mb-6">
            <!-- Rating -->
            <div class="flex items-center">
              <svg class="w-5 h-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              <span class="text-gray-600 ml-1">{{ classData.rating }}</span>
            </div>

            <!-- Students -->
            <div class="flex items-center">
              <svg class="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
              </svg>
              <span class="text-gray-600 ml-1">{{ classData.studentsEnrolled }} Students Enrolled</span>
            </div>

            <!-- Modules -->
            <div class="flex items-center">
              <svg class="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
              </svg>
              <span class="text-gray-600 ml-1">{{ classData.modules }} Modules</span>
            </div>

            <!-- Posted Date -->
            <div class="flex items-center">
              <svg class="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
              </svg>
              <span class="text-gray-600 ml-1">{{ formatDate(classData.postedDate) }}, {{ classData.postedTime }}</span>
            </div>
          </div>

          <!-- Join Class Button - Positioned at top right -->
          <button
            v-if="!isClassJoined"
            @click="joinClass"
            class="absolute top-0 right-0 py-2.5 px-6 bg-orange text-white font-medium rounded-lg hover:bg-orange-600 transition-colors shadow-sm"
          >
            Join Class
          </button>
          <div
            v-else
            class="absolute top-0 right-0 py-2.5 px-6 bg-gray-100 text-gray-600 font-medium rounded-lg text-center flex items-center justify-center"
          >
            <svg class="w-5 h-5 mr-2 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <span>Already Joined</span>
          </div>

          <!-- What you will get section -->
          <div class="mt-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">What you will get</h2>
            <div class="flex flex-nowrap overflow-x-auto space-x-4 pb-2">
              <!-- E-Certificate -->
              <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow flex-shrink-0 w-48">
                <div class="flex items-center mb-2">
                  <div class="w-7 h-7 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <span class="font-medium text-sm">E-Certificate</span>
                </div>
                <p class="text-xs text-gray-500">Earn a certificate upon completion of this class.</p>
              </div>

              <!-- Modules -->
              <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow flex-shrink-0 w-48">
                <div class="flex items-center mb-2">
                  <div class="w-7 h-7 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                    </svg>
                  </div>
                  <span class="font-medium text-sm">Modules</span>
                </div>
                <p class="text-xs text-gray-500">Reading materials are presented in easy-to-understand.</p>
              </div>

              <!-- Recording -->
              <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow flex-shrink-0 w-48">
                <div class="flex items-center mb-2">
                  <div class="w-7 h-7 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                    </svg>
                  </div>
                  <span class="font-medium text-sm">Recording</span>
                </div>
                <p class="text-xs text-gray-500">Access video to review course content.</p>
              </div>

              <!-- Submission -->
              <div class="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow flex-shrink-0 w-48">
                <div class="flex items-center mb-2">
                  <div class="w-7 h-7 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <span class="font-medium text-sm">Submission</span>
                </div>
                <p class="text-xs text-gray-500">Test your technical skills by completing submission assignments.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="mb-6">
        <div class="flex border-b border-gray-200 relative">
          <button
            v-for="tab in ['description', 'tools', 'modules']"
            :key="tab"
            @click="activeTab = tab"
            class="py-2 px-4 font-medium text-md transition-colors relative"
            :class="activeTab === tab ? 'border-2 border-orange text-orange rounded-lg bg-white' : 'border-b-2 border-transparent text-gray-500 hover:text-gray-700'"
          >
            {{ tab === 'description' ? 'Class Description' : tab === 'tools' ? 'Learning Tools' : 'Modules' }}
          </button>
        </div>
      </div>

      <!-- Tab Content -->
      <div v-if="activeTab === 'description'" class="space-y-6">
        <div>
          <h3 class="text-xl font-semibold text-gray-800 mb-3">Description</h3>
          <p class="text-gray-600 leading-relaxed">
            {{ classData.description }}
          </p>
        </div>

        <div>
          <h3 class="text-xl font-semibold text-gray-800 mb-3">Targets and Goals</h3>
          <p class="text-gray-600 leading-relaxed mb-4">
            {{ classData.targetsAndGoals ? classData.targetsAndGoals.description : `This class is designed to help you master the fundamentals and advanced concepts of ${getCategoryDescription(classData.category)}. You'll learn through hands-on projects and practical examples that will prepare you for real-world applications.` }}
          </p>
          <ul class="list-disc pl-5 space-y-2 text-gray-600">
            <!-- Use hardcoded goals if available -->
            <template v-if="classData.targetsAndGoals && classData.targetsAndGoals.goals">
              <li v-for="(goal, index) in classData.targetsAndGoals.goals" :key="index">{{ goal }}</li>
            </template>
            <!-- Fallback to default goals if not available -->
            <template v-else>
              <li v-if="classData.category === 'frontend'">Master frontend development concepts and user interface design</li>
              <li v-if="classData.category === 'backend'">Understand server-side programming and API development</li>
              <li v-if="classData.category === 'mobile'">Learn mobile app development for iOS and Android platforms</li>
              <li>Build a portfolio of projects to showcase your skills</li>
              <li>Gain practical experience through hands-on assignments</li>
              <li>Learn industry-standard tools and workflows</li>
              <li>Develop problem-solving skills through real-world challenges</li>
              <li>Receive feedback from experienced mentors</li>
            </template>
          </ul>
        </div>
      </div>

      <div v-if="activeTab === 'tools'" class="space-y-4">
        <h3 class="text-xl font-semibold text-gray-800 mb-3">Learning Tools</h3>

        <!-- Minimum device specifications -->
        <div class="mb-6">
          <p class="text-gray-800 font-medium mb-2">Minimum device specifications:</p>
          <ul class="list-disc pl-5 space-y-1 text-gray-700">
            <li class="flex items-start">
              <span class="font-medium mr-1">Processor</span>
            </li>
          </ul>
          <p class="text-gray-700 ml-5">
            {{ classData.learningTools && classData.learningTools.deviceSpecs && classData.learningTools.deviceSpecs.processor
              ? classData.learningTools.deviceSpecs.processor
              : "Intel Dual Core (Core i3 and above recommended)" }}
          </p>
        </div>

        <!-- Tools needed for learning -->
        <div>
          <p class="text-gray-800 font-medium mb-2">Tools needed for learning:</p>

          <!-- Use hardcoded tools if available -->
          <template v-if="classData.learningTools && classData.learningTools.tools && classData.learningTools.tools.length > 0">
            <ul class="list-disc pl-5 space-y-1 text-gray-700">
              <li v-for="(tool, index) in classData.learningTools.tools" :key="index" class="flex items-start">
                <span class="font-medium mr-1">{{ tool }}</span>
              </li>
            </ul>
            <p v-if="classData.learningTools.tools.includes('Google Collaboratory')" class="text-gray-700 ml-5">How to use it will be taught in this class.</p>
          </template>

          <!-- Fallback to default tools if not available -->
          <template v-else>
            <ul class="list-disc pl-5 space-y-1 text-gray-700">
              <li class="flex items-start">
                <span class="font-medium mr-1">Google Collaboratory</span>
              </li>
            </ul>
            <p class="text-gray-700 ml-5">How to use it will be taught in this class.</p>

            <!-- Additional tools based on class category -->
            <ul v-if="classData.category === 'frontend'" class="list-disc pl-5 space-y-1 text-gray-700 mt-2">
              <li class="flex items-start">
                <span class="font-medium mr-1">Visual Studio Code</span>
              </li>
              <li class="flex items-start">
                <span class="font-medium mr-1">Node.js</span>
              </li>
              <li class="flex items-start">
                <span class="font-medium mr-1">Figma</span>
              </li>
            </ul>

            <ul v-if="classData.category === 'backend'" class="list-disc pl-5 space-y-1 text-gray-700 mt-2">
              <li class="flex items-start">
                <span class="font-medium mr-1">Visual Studio Code</span>
              </li>
              <li v-if="classData.title.includes('Node')" class="flex items-start">
                <span class="font-medium mr-1">Node.js</span>
              </li>
              <li class="flex items-start">
                <span class="font-medium mr-1">Postman</span>
              </li>
            </ul>

            <ul v-if="classData.category === 'mobile'" class="list-disc pl-5 space-y-1 text-gray-700 mt-2">
              <li class="flex items-start">
                <span class="font-medium mr-1">Android Studio / Xcode</span>
              </li>
              <li v-if="classData.title.includes('Flutter')" class="flex items-start">
                <span class="font-medium mr-1">Flutter SDK</span>
              </li>
              <li v-else-if="classData.title.includes('React Native')" class="flex items-start">
                <span class="font-medium mr-1">React Native CLI</span>
              </li>
              <li class="flex items-start">
                <span class="font-medium mr-1">Emulator/Simulator</span>
              </li>
            </ul>
          </template>
        </div>

        <!-- Additional requirements -->
        <div v-if="classData.learningTools && classData.learningTools.additionalRequirements && classData.learningTools.additionalRequirements.length > 0" class="mt-4">
          <p class="text-gray-800 font-medium mb-2">Additional requirements:</p>
          <ul class="list-disc pl-5 space-y-1 text-gray-700">
            <li v-for="(req, index) in classData.learningTools.additionalRequirements" :key="index" class="flex items-start">
              <span v-if="req.includes('-')" v-html="req.replace('-', '<span class=\'font-medium mr-1\'>' + req.split('-')[0].trim() + '</span> -') + req.split('-')[1]"></span>
              <span v-else class="font-medium mr-1">{{ req }}</span>
            </li>
          </ul>
        </div>

        <!-- Fallback additional requirements for mobile classes -->
        <div v-else-if="classData.category === 'mobile'" class="mt-4">
          <p class="text-gray-800 font-medium mb-2">Additional requirements:</p>
          <ul class="list-disc pl-5 space-y-1 text-gray-700">
            <li class="flex items-start">
              <span class="font-medium mr-1">Smartphone</span> - Android or iOS device for testing
            </li>
          </ul>
        </div>
      </div>

      <div v-if="activeTab === 'modules'" class="space-y-4">
        <!-- Modules content -->
        <div class="space-y-6">
          <div v-for="i in classData.modules" :key="i" class="pb-6 border-b border-gray-200">
            <h4 class="text-xl font-semibold text-gray-800 mb-4">{{ getModuleTitle(classData.category, i) }}</h4>
            <p class="text-gray-600 mb-4">
              {{ getModuleDescription(classData.category, i) }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <!-- Success Modal -->
    <div v-if="showSuccessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Successfully Joined!</h3>
          <p class="text-sm text-gray-500 mb-4">
            You have successfully joined the class. You can now access it from your Academy page.
          </p>
          <div class="flex justify-center space-x-3">
            <button
              @click="showSuccessModal = false"
              class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            >
              Close
            </button>
            <button
              @click="goToClass"
              class="px-4 py-2 bg-orange text-white rounded-md hover:bg-orange-600 transition-colors"
            >
              Go to Class
            </button>
          </div>
        </div>
      </div>
    </div>

    <FooterStudent />
  </div>
</template>

<script setup>
/**
 * ClassDetail.vue - Displays details of a class from the available classes
 *
 * Data Structure for Admin Reference:
 * - Class data should include: id, title, rating, studentsEnrolled, modules, category,
 *   description, postedDate, postedTime
 * - Category should be one of: 'frontend', 'backend', 'mobile'
 * - Module titles should be descriptive and reflect the content of each module
 * - Class descriptions should be detailed and explain what students will learn
 * - All data should be synchronized across related files to maintain consistency
 */

import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import NavbarStudent from '@/components/@student/NavbarStudent.vue';
import FooterStudent from '@/components/@student/FooterStudent.vue';
import { useClassStore, STORAGE_KEYS } from '@/data/availableClasses';
import { formatDate } from '@/utils/studentUtils';

const route = useRoute();
const router = useRouter();
const classStore = useClassStore();

// State
const classData = ref({});
const activeTab = ref('description');
const showSuccessModal = ref(false);
const joinedClassId = ref(null);
const classImage = ref(null);

// Get class ID from route params or query
const classId = ref(parseInt(route.params.classId || route.query.classId));

// Check if the class has already been joined
const isClassJoined = computed(() => {
  const joinedClasses = classStore.classes.value;

  // Check by both ID and title
  return joinedClasses.some(c =>
    // Check by title (case-insensitive)
    c.title.toLowerCase() === (classData.value.title || '').toLowerCase() ||
    // Check by ID if available
    (classData.value.id && c.id === classData.value.id)
  );
});

// formatDate is now imported from studentUtils.js

// Get default image based on category
const getDefaultImageForCategory = (category) => {
  const categoryImages = {
    'frontend': '/class-design.png',
    'backend': '/class-golang.png',
    'mobile': '/class-data.png'
  };
  return categoryImages[category] || '/class-golang.png';
};

// Get appropriate image based on class data
const getClassImage = (classData) => {
  return classData.imageUrl || getDefaultImageForCategory(classData.category);
};

// Handle image loading error
const handleImageError = (event) => {
  if (classData.value && classData.value.category) {
    event.target.src = getDefaultImageForCategory(classData.value.category);
  } else {
    event.target.src = '/class-golang.png';
  }
};

// Get category description from class data
const getCategoryDescription = (category) => {
  // If we have class data with a categoryDescription, use that
  if (classData.value && classData.value.categoryDescription) {
    return classData.value.categoryDescription;
  }

  // Fallback descriptions if class data doesn't have categoryDescription
  const descriptions = {
    'frontend': 'frontend development, including modern JavaScript frameworks, responsive design, and user interface implementation. You\'ll learn how to create engaging, interactive web applications with clean, maintainable code.',
    'backend': 'backend development, covering server-side programming, API design, database management, and scalable architecture. You\'ll develop skills in building robust, secure, and efficient server applications.',
    'mobile': 'mobile app development, exploring cross-platform and native solutions for iOS and Android. You\'ll learn to create responsive, feature-rich applications that provide excellent user experiences across different devices.'
  };

  return descriptions[category] || 'this subject area, providing you with comprehensive knowledge and practical skills that are highly valued in the industry. The curriculum is designed to balance theoretical foundations with hands-on experience.';
};

// Define default module data for all categories
const getDefaultModuleData = () => {
  return {
    'frontend': {
      titles: [
        'HTML5 & CSS3 Fundamentals',
        'JavaScript Essentials',
        'Responsive Design',
        'Modern JavaScript Frameworks',
        'State Management',
        'Performance Optimization'
      ],
      description: 'Master frontend development concepts and create responsive web applications in this hands-on module.'
    },
    'backend': {
      titles: [
        'Introduction to Backend Development',
        'RESTful API Design',
        'Database Management',
        'Authentication & Security',
        'Performance Optimization',
        'Deployment & Scaling'
      ],
      description: 'Learn essential backend concepts and practical applications in this comprehensive module.'
    },
    'mobile': {
      titles: [
        'Mobile Development Fundamentals',
        'UI Components & Layouts',
        'Navigation & State Management',
        'Data Persistence & API Integration',
        'Native Device Features',
        'App Store Deployment'
      ],
      description: 'Explore mobile app development techniques and build applications for iOS and Android platforms in this interactive module.'
    },
    'default': {
      titles: [
        'Module 1: Introduction to Core Concepts',
        'Module 2: Fundamental Principles',
        'Module 3: Intermediate Concepts',
        'Module 4: Advanced Topics',
        'Module 5: Practical Applications',
        'Module 6: Industry Best Practices'
      ],
      description: 'Comprehensive learning module with practical applications and hands-on exercises.'
    }
  };
};

// Get module title based on class data and module number
const getModuleTitle = (category, moduleNumber) => {
  // If we have class data with moduleData, use that
  if (classData.value && classData.value.moduleData && classData.value.moduleData.length >= moduleNumber) {
    return classData.value.moduleData[moduleNumber - 1].title;
  }

  // Get default module data
  const modulesByCategory = getDefaultModuleData();

  // Get the appropriate modules list or default if category not found
  const categoryData = modulesByCategory[category] || modulesByCategory.default;

  // Return the module title if it exists, otherwise return a default title
  if (moduleNumber <= categoryData.titles.length) {
    return categoryData.titles[moduleNumber - 1];
  } else {
    // For module numbers beyond our predefined list, generate a sensible title
    const capitalizedCategory = category.charAt(0).toUpperCase() + category.slice(1);
    return `Module ${moduleNumber}: Advanced ${capitalizedCategory}`;
  }
};

// Get module description based on class data and module number
const getModuleDescription = (category, moduleNumber) => {
  // If we have class data with moduleData, use that
  if (classData.value && classData.value.moduleData && classData.value.moduleData.length >= moduleNumber) {
    return classData.value.moduleData[moduleNumber - 1].description;
  }

  // Get default module data
  const modulesByCategory = getDefaultModuleData();

  // Get the appropriate description or a default one
  return (modulesByCategory[category] || modulesByCategory.default).description;
};



// Join class function
const joinClass = () => {
  // Double-check if class is already joined to prevent duplicates
  if (isClassJoined.value) {
    console.warn('Class is already joined! Showing success modal directly.');
    showSuccessModal.value = true;

    // Find the existing class ID for navigation
    const existingClass = classStore.classes.value.find(c =>
      c.title === classData.value.title || c.id === classData.value.id
    );

    if (existingClass) {
      joinedClassId.value = existingClass.id;
    }

    return;
  }

  // Get current date and time for proper data synchronization
  const now = new Date();
  const formattedDate = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
  const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

  // Use learning tools from class data if available, otherwise create new ones
  let learningTools;

  if (classData.value && classData.value.learningTools) {
    // Use existing learning tools data
    learningTools = { ...classData.value.learningTools };
  } else {
    // Create learning tools data based on class category
    learningTools = {
      deviceSpecs: {
        processor: "Intel Dual Core (Core i3 and above recommended)"
      },
      tools: ["Google Collaboratory"]
    };

    // Add category-specific tools using a more efficient approach
    const categoryTools = {
      'frontend': ["Visual Studio Code", "Node.js", "Figma"],
      'backend': ["Visual Studio Code", "Postman"],
      'mobile': ["Android Studio / Xcode", "Emulator/Simulator"]
    };

    // Add base tools for the category
    if (categoryTools[classData.value.category]) {
      learningTools.tools.push(...categoryTools[classData.value.category]);
    }

    // Add specific tools based on class title and category
    if (classData.value.category === 'backend' && classData.value.title.includes('Node')) {
      learningTools.tools.push("Node.js");
    } else if (classData.value.category === 'mobile') {
      if (classData.value.title.includes('Flutter')) {
        learningTools.tools.push("Flutter SDK");
      } else if (classData.value.title.includes('React Native')) {
        learningTools.tools.push("React Native CLI");
      }
    }

    // Add additional requirements for mobile classes
    if (classData.value.category === 'mobile') {
      learningTools.additionalRequirements = ["Smartphone - Android or iOS device for testing"];
    }
  }

  // Generate module-specific data for better synchronization
  const moduleData = [];

  // Generate module data using the same logic as getModuleTitle and getModuleDescription
  for (let i = 1; i <= classData.value.modules; i++) {
    const category = classData.value.category;

    // Use the same functions we already have to get title and description
    const moduleTitle = getModuleTitle(category, i);
    const moduleDescription = getModuleDescription(category, i);

    moduleData.push({
      title: moduleTitle,
      description: moduleDescription
    });
  }

  // Create a new class in the class store with all necessary data
  const newClassId = classStore.addClass({
    title: classData.value.title,
    rating: classData.value.rating,
    description: classData.value.description,
    modules: classData.value.modules,
    studentsEnrolled: classData.value.studentsEnrolled,
    category: classData.value.category,
    postedDate: classData.value.postedDate,
    postedTime: classData.value.postedTime,
    imageUrl: classData.value.imageUrl,
    joinedDate: formattedDate,
    joinedTime: formattedTime,
    status: 'studied',
    learningTools,
    moduleData,
    progress: 0,
    // Use existing students data if available, otherwise generate mock students
    mockStudents: classData.value.students || generateMockStudents(classData.value.studentsEnrolled)
  });

  // Store the joined class ID for navigation
  joinedClassId.value = newClassId;

  // Show success modal
  showSuccessModal.value = true;
};

// Helper function to generate mock student data for leaderboard
const generateMockStudents = (count) => {
  // If class data has students property, use it
  if (classData.value && classData.value.students && classData.value.students.length > 0) {
    // Add profile pictures if missing
    return classData.value.students.map(student => ({
      ...student,
      profilePicture: student.profilePicture || getRandomProfilePicture(student.id)
    }));
  }

  // Sample data for students (simplified)
  const studentData = [
    { id: 1, name: 'John Smith', profilePicture: 'https://randomuser.me/api/portraits/men/32.jpg', mark: 92 },
    { id: 2, name: 'Emma Johnson', profilePicture: 'https://randomuser.me/api/portraits/women/82.jpg', mark: 95 },
    { id: 3, name: 'Michael Brown', profilePicture: 'https://randomuser.me/api/portraits/men/75.jpg', mark: 88 },
    { id: 4, name: 'Sophia Davis', profilePicture: 'https://randomuser.me/api/portraits/women/44.jpg', mark: 91 },
    { id: 5, name: 'William Wilson', profilePicture: 'https://randomuser.me/api/portraits/men/21.jpg', mark: 85 },
    { id: 6, name: 'Olivia Martinez', profilePicture: 'https://randomuser.me/api/portraits/women/68.jpg', mark: 93 },
    { id: 7, name: 'James Taylor', profilePicture: 'https://randomuser.me/api/portraits/men/54.jpg', mark: 87 },
    { id: 8, name: 'Ava Anderson', profilePicture: 'https://randomuser.me/api/portraits/women/17.jpg', mark: 94 }
  ];

  // Return a subset of students based on count
  return studentData.slice(0, Math.min(count, studentData.length));
};

// Helper function to get a random profile picture
const getRandomProfilePicture = (id) => {
  const gender = id % 2 === 0 ? 'women' : 'men';
  const number = (id * 13) % 99; // Generate a pseudo-random number between 0-99
  return `https://randomuser.me/api/portraits/${gender}/${number}.jpg`;
};

// Navigate to the joined class
const goToClass = () => {
  if (joinedClassId.value) {
    // Set current class
    classStore.setCurrentClass(joinedClassId.value);

    // Store the source tab as 'studied' in localStorage
    localStorage.setItem(STORAGE_KEYS.CLASS_SOURCE, 'studied');

    // Navigate to the class detail page without forcing a reload
    router.push({
      name: 'DetailClass',
      params: { classId: joinedClassId.value }
    });
  } else {
    // Navigate to academy page if no class ID
    router.push('/student/academy');
  }

  // Close the modal
  showSuccessModal.value = false;
};

// Fetch class data on mount
onMounted(async () => {
  try {
    // Import the available classes data and categories
    const { dummyAvailableClassesData, classesByCategory } = await import('@/data/dummyAvailableClasses.js');

    // Convert class ID to number for consistent comparison
    const numericClassId = Number(classId.value);

    // Find class using different strategies
    let foundClass = findClassById(dummyAvailableClassesData, numericClassId);

    if (foundClass) {
      classData.value = { ...foundClass };
    } else {
      // Try to find class by category and ID
      foundClass = findClassByCategoryAndId(classesByCategory, numericClassId);

      if (foundClass) {
        classData.value = { ...foundClass };
      } else {
        classData.value = createFallbackClassData(numericClassId, dummyAvailableClassesData);
      }
    }
  } catch (error) {
    console.error("Error loading class data:", error);
    // Create fallback data in case of error
    classData.value = createErrorFallbackData(classId.value);
  }
});

// Helper function to find class by ID - simplified
const findClassById = (classes, id) => {
  // Direct match by ID
  let foundClass = classes.find(c => c.id === id);
  if (foundClass) return foundClass;

  // Fallback: Find by index if ID is within range
  if (id >= 1 && id <= classes.length) {
    return classes[id - 1];
  }

  return null;
};

// Helper function to determine category from ID
const getCategoryFromId = (id) => {
  if (id >= 100 && id < 200) return 'frontend';
  if (id >= 200 && id < 300) return 'backend';
  if (id >= 300 && id < 400) return 'mobile';
  return 'frontend'; // Default to frontend for any other ID range
};

// Helper function to find class by category and ID
const findClassByCategoryAndId = (classesByCategory, id) => {
  // Determine category based on ID range using the helper function
  const category = getCategoryFromId(id);

  // Look for the class in that category
  if (classesByCategory[category]) {
    return classesByCategory[category].find(c => c.id === id);
  }

  return null;
};

// Create fallback class data when class not found - simplified
const createFallbackClassData = (id, availableClasses) => {
  // Use first available class if possible
  if (availableClasses.length > 0) {
    return {
      ...availableClasses[0],
      id: id // Keep the requested ID for consistency
    };
  }

  // Otherwise generate default data based on ID range
  const defaultCategory = getCategoryFromId(id);
  const defaultTitle = `${defaultCategory.charAt(0).toUpperCase() + defaultCategory.slice(1)} Development Course`;

  return {
    id: id,
    title: defaultTitle,
    rating: 4.7,
    studentsEnrolled: 75,
    modules: 5,
    category: defaultCategory,
    description: `Learn modern ${defaultCategory} development techniques and frameworks. This course covers essential concepts and tools to help you build professional applications.`,
    postedDate: "2025-04-15",
    postedTime: "10:00 AM",
    imageUrl: getDefaultImageForCategory(defaultCategory)
  };
};

// Create fallback data in case of error - simplified
const createErrorFallbackData = (id) => {
  // Reuse the fallback class data function with empty array
  return createFallbackClassData(id, []);
};
</script>